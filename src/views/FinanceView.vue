<template>
  <div class="finance-view">
    <!-- 财务概览 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="card-title">总收入</div>
              <div class="card-value">¥ 1,234,567</div>
              <div class="card-trend">
                <span class="positive">↑ 12%</span> 同比上月
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="card-title">总支出</div>
              <div class="card-value">¥ 234,567</div>
              <div class="card-trend">
                <span class="negative">↓ 5%</span> 同比上月
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="card-title">订单总数</div>
              <div class="card-value">1,234</div>
              <div class="card-trend">
                <span class="positive">↑ 8%</span> 同比上月
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="card-title">退款金额</div>
              <div class="card-value">¥ 12,345</div>
              <div class="card-trend">
                <span class="negative">↓ 2%</span> 同比上月
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select v-model="searchForm.transactionType" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option label="收入" value="income"></el-option>
            <el-option label="支出" value="expense"></el-option>
            <el-option label="退款" value="refund"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 财务明细表格 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column prop="transactionNo" label="交易编号" width="180"></el-table-column>
      <el-table-column prop="orderNo" label="关联订单号" width="180"></el-table-column>
      <el-table-column prop="transactionType" label="交易类型">
        <template slot-scope="scope">
          <el-tag :type="getTagType(scope.row.transactionType)">
            {{ formatTransactionType(scope.row.transactionType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额(元)" align="right"></el-table-column>
      <el-table-column prop="transactionTime" label="交易时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
            {{ scope.row.status === 'success' ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注"></el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FinanceView',
  data() {
    return {
      searchForm: {
        dateRange: [],
        transactionType: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  created() {
    this.getFinanceData()
  },
  methods: {
    // 获取财务数据
    getFinanceData() {
      // TODO: 调用接口获取财务数据
      this.tableData = [
        {
          transactionNo: 'TX202405010001',
          orderNo: 'ORD202405010001',
          transactionType: 'income',
          amount: '299.00',
          transactionTime: '2024-05-01 10:30:45',
          status: 'success',
          remark: '充电服务费'
        },
        {
          transactionNo: 'TX202405010002',
          orderNo: 'ORD202405010002',
          transactionType: 'refund',
          amount: '-50.00',
          transactionTime: '2024-05-01 11:15:22',
          status: 'success',
          remark: '用户申请退款'
        }
        // 更多数据...
      ]
      this.total = 100 // 总条数
    },
    // 格式化交易类型
    formatTransactionType(type) {
      const map = {
        income: '收入',
        expense: '支出',
        refund: '退款'
      }
      return map[type] || type
    },
    // 获取标签类型
    getTagType(type) {
      const map = {
        income: 'success',
        expense: 'danger',
        refund: 'warning'
      }
      return map[type] || ''
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.getFinanceData()
    },
    // 重置
    handleReset() {
      this.searchForm = {
        dateRange: [],
        transactionType: ''
      }
      this.handleSearch()
    },
    // 导出
    handleExport() {
      // TODO: 实现导出功能
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.pageSize = val
      this.getFinanceData()
    },
    // 改变页码
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFinanceData()
    }
  }
}
</script>

<style scoped>
.finance-view {
  padding: 20px;
}
.overview-cards {
  margin-bottom: 20px;
}
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.card-content {
  text-align: center;
}
.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 12px;
}
.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}
.card-trend {
  font-size: 12px;
}
.positive {
  color: #67c23a;
}
.negative {
  color: #f56c6c;
}
</style>