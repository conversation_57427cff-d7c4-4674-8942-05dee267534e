<template>
  <div class="user-profile">
    <el-row :gutter="20">
      <!-- 左侧个人信息 -->
      <el-col :span="16">
        <el-card class="profile-card">
          <div slot="header" class="card-header">
            <span>个人信息</span>
            <el-button type="primary" size="small" @click="handleEdit" v-if="!isEditing">编辑</el-button>
            <div v-else>
              <el-button size="small" @click="handleCancel">取消</el-button>
              <el-button type="primary" size="small" @click="handleSave">保存</el-button>
            </div>
          </div>
          
          <div v-if="!isEditing" class="profile-info">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>用户ID：</label>
                  <span>{{ userInfo.userId }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>用户姓名：</label>
                  <span>{{ userInfo.userName }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>手机号：</label>
                  <span>{{ userInfo.phone }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>邮箱：</label>
                  <span>{{ userInfo.email }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>注册时间：</label>
                  <span>{{ userInfo.createTime }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>最后登录：</label>
                  <span>{{ userInfo.lastLoginTime }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div v-else class="profile-edit">
            <el-form :model="editForm" :rules="formRules" ref="profileForm" label-width="80px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用户姓名" prop="userName">
                    <el-input v-model="editForm.userName" placeholder="请输入用户姓名"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号" prop="phone">
                    <el-input v-model="editForm.phone" placeholder="请输入手机号"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="editForm.email" placeholder="请输入邮箱"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>

        <!-- 修改密码 -->
        <el-card class="password-card">
          <div slot="header">
            <span>修改密码</span>
          </div>
          <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
            <el-form-item label="当前密码" prop="oldPassword">
              <el-input type="password" v-model="passwordForm.oldPassword" placeholder="请输入当前密码"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
              <el-input type="password" v-model="passwordForm.newPassword" placeholder="请输入新密码"></el-input>
            </el-form-item>
            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input type="password" v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handlePasswordChange">修改密码</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧账户信息 -->
      <el-col :span="8">
        <el-card class="account-card">
          <div slot="header">
            <span>账户信息</span>
          </div>
          <div class="account-info">
            <div class="balance-item">
              <div class="balance-value">{{ userInfo.balance }}元</div>
              <div class="balance-label">账户余额</div>
              <el-button type="primary" size="small" @click="handleRecharge">充值</el-button>
            </div>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ stats.totalOrders }}</div>
                <div class="stat-label">总订单数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ stats.totalPower }}</div>
                <div class="stat-label">总充电量(度)</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ stats.totalAmount }}</div>
                <div class="stat-label">总消费(元)</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ stats.loginCount }}</div>
                <div class="stat-label">登录次数</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 快捷操作 -->
        <el-card class="quick-actions">
          <div slot="header">
            <span>快捷操作</span>
          </div>
          <div class="actions-grid">
            <el-button type="primary" icon="el-icon-money" @click="viewOrders">我的订单</el-button>
            <el-button type="success" icon="el-icon-wallet" @click="viewBalance">余额明细</el-button>
            <el-button type="warning" icon="el-icon-location" @click="viewStations">附近电站</el-button>
            <el-button type="info" icon="el-icon-service" @click="contactService">联系客服</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 充值弹窗 -->
    <el-dialog title="账户充值" :visible.sync="rechargeVisible" width="400px">
      <el-form :model="rechargeForm" label-width="80px">
        <el-form-item label="充值金额">
          <el-input-number 
            v-model="rechargeForm.amount" 
            :min="1" 
            :max="10000" 
            :precision="2"
            placeholder="请输入充值金额">
          </el-input-number>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-radio-group v-model="rechargeForm.payMethod">
            <el-radio label="alipay">支付宝</el-radio>
            <el-radio label="wechat">微信支付</el-radio>
            <el-radio label="bank">银行卡</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rechargeVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRechargeSubmit">确认充值</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      userInfo: {},
      stats: {},
      isEditing: false,
      editForm: {},
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rechargeForm: {
        amount: 100,
        payMethod: 'alipay'
      },
      rechargeVisible: false,
      formRules: {
        userName: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
    this.getStats()
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      // TODO: 调用接口获取当前用户信息
      this.userInfo = {
        userId: 'U001',
        userName: '张三',
        phone: '13800138001',
        email: '<EMAIL>',
        balance: '1250.50',
        createTime: '2024-01-15 10:30:00',
        lastLoginTime: '2025-01-20 14:25:30'
      }
    },
    // 获取统计信息
    getStats() {
      // TODO: 调用接口获取统计信息
      this.stats = {
        totalOrders: 89,
        totalPower: '1250.5',
        totalAmount: '3749.50',
        loginCount: 156
      }
    },
    // 编辑个人信息
    handleEdit() {
      this.editForm = { ...this.userInfo }
      this.isEditing = true
    },
    // 取消编辑
    handleCancel() {
      this.isEditing = false
      this.editForm = {}
    },
    // 保存个人信息
    handleSave() {
      this.$refs.profileForm.validate((valid) => {
        if (valid) {
          // TODO: 调用接口保存用户信息
          this.userInfo = { ...this.editForm }
          this.isEditing = false
          this.$message.success('保存成功')
        }
      })
    },
    // 修改密码
    handlePasswordChange() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          // TODO: 调用接口修改密码
          this.$message.success('密码修改成功')
          this.passwordForm = {
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
          }
        }
      })
    },
    // 验证确认密码
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },
    // 充值
    handleRecharge() {
      this.rechargeVisible = true
    },
    // 提交充值
    handleRechargeSubmit() {
      // TODO: 调用接口处理充值
      this.$message.success('充值成功')
      this.rechargeVisible = false
      this.getUserInfo() // 刷新用户信息
    },
    // 查看订单
    viewOrders() {
      this.$router.push('/orders')
    },
    // 查看余额明细
    viewBalance() {
      this.$router.push('/balance-records')
    },
    // 查看附近电站
    viewStations() {
      this.$router.push('/stations')
    },
    // 联系客服
    contactService() {
      this.$message.info('客服功能开发中...')
    }
  }
}
</script>

<style scoped>
.user-profile {
  padding: 20px;
}
.profile-card, .password-card, .account-card, .quick-actions {
  margin-bottom: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.info-item {
  margin-bottom: 15px;
}
.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}
.balance-item {
  text-align: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}
.balance-value {
  font-size: 28px;
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 8px;
}
.balance-label {
  color: #909399;
  margin-bottom: 15px;
}
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}
.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}
.stat-label {
  color: #909399;
  font-size: 12px;
}
.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}
.actions-grid .el-button {
  width: 100%;
}
</style>
