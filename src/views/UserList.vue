<template>
  <div class="user-list">
    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="用户账号">
          <el-input v-model="searchForm.userId" placeholder="请输入用户账号"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input v-model="searchForm.userName" placeholder="请输入用户姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="用户状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="正常" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAdd">新增用户</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 用户表格 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column prop="userId" label="用户ID" width="120"></el-table-column>
      <el-table-column prop="userName" label="用户姓名" width="120"></el-table-column>
      <el-table-column prop="phone" label="手机号" width="140"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
      <el-table-column prop="balance" label="账户余额(元)" width="120"></el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
            {{ scope.row.status === '1' ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="注册时间" width="160"></el-table-column>
      <el-table-column prop="lastLoginTime" label="最后登录" width="160"></el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            type="text" 
            :class="scope.row.status === '1' ? 'danger' : 'success'"
            @click="handleToggleStatus(scope.row)">
            {{ scope.row.status === '1' ? '禁用' : '启用' }}
          </el-button>
          <el-button type="text" class="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 用户详情弹窗 -->
    <el-dialog title="用户详情" :visible.sync="detailVisible" width="600px">
      <div v-if="currentUser" class="user-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>用户ID：</label>
              <span>{{ currentUser.userId }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>用户姓名：</label>
              <span>{{ currentUser.userName }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>手机号：</label>
              <span>{{ currentUser.phone }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>邮箱：</label>
              <span>{{ currentUser.email }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>账户余额：</label>
              <span>{{ currentUser.balance }}元</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="currentUser.status === '1' ? 'success' : 'danger'">
                {{ currentUser.status === '1' ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>注册时间：</label>
              <span>{{ currentUser.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>最后登录：</label>
              <span>{{ currentUser.lastLoginTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 新增/编辑用户弹窗 -->
    <el-dialog :title="isEdit ? '编辑用户' : '新增用户'" :visible.sync="formVisible" width="500px">
      <el-form :model="userForm" :rules="formRules" ref="userForm" label-width="80px">
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="userForm.userName" placeholder="请输入用户姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="初始余额" prop="balance" v-if="!isEdit">
          <el-input-number v-model="userForm.balance" :min="0" :precision="2" placeholder="请输入初始余额"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio label="1">正常</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserList',
  data() {
    return {
      searchForm: {
        userId: '',
        userName: '',
        phone: '',
        status: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      detailVisible: false,
      formVisible: false,
      isEdit: false,
      currentUser: null,
      userForm: {
        userName: '',
        phone: '',
        email: '',
        balance: 0,
        status: '1'
      },
      formRules: {
        userName: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getUserList()
  },
  methods: {
    // 获取用户列表
    getUserList() {
      // TODO: 调用接口获取用户列表数据
      this.tableData = [
        {
          userId: 'U001',
          userName: '张三',
          phone: '13800138001',
          email: '<EMAIL>',
          balance: '1250.50',
          status: '1',
          createTime: '2024-01-15 10:30:00',
          lastLoginTime: '2025-01-20 14:25:30'
        },
        {
          userId: 'U002',
          userName: '李四',
          phone: '13800138002',
          email: '<EMAIL>',
          balance: '890.00',
          status: '1',
          createTime: '2024-02-20 09:15:00',
          lastLoginTime: '2025-01-19 16:45:20'
        },
        {
          userId: 'U003',
          userName: '王五',
          phone: '13800138003',
          email: '<EMAIL>',
          balance: '0.00',
          status: '0',
          createTime: '2024-03-10 14:20:00',
          lastLoginTime: '2025-01-10 11:30:15'
        }
      ]
      this.total = 3
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.getUserList()
    },
    // 重置
    handleReset() {
      this.searchForm = {
        userId: '',
        userName: '',
        phone: '',
        status: ''
      }
      this.handleSearch()
    },
    // 新增用户
    handleAdd() {
      this.isEdit = false
      this.userForm = {
        userName: '',
        phone: '',
        email: '',
        balance: 0,
        status: '1'
      }
      this.formVisible = true
    },
    // 查看详情
    handleDetail(row) {
      this.$router.push(`/users/${row.userId}`)
    },
    // 编辑用户
    handleEdit(row) {
      this.isEdit = true
      this.userForm = { ...row }
      this.formVisible = true
    },
    // 切换用户状态
    handleToggleStatus(row) {
      const action = row.status === '1' ? '禁用' : '启用'
      this.$confirm(`确定要${action}该用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用接口切换用户状态
        row.status = row.status === '1' ? '0' : '1'
        this.$message.success(`${action}成功`)
      })
    },
    // 删除用户
    handleDelete(row) {
      this.$confirm('确定要删除该用户吗？删除后不可恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用接口删除用户
        this.$message.success('删除成功')
        this.getUserList()
      })
    },
    // 提交表单
    handleSubmit() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          // TODO: 调用接口保存用户信息
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.formVisible = false
          this.getUserList()
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.pageSize = val
      this.getUserList()
    },
    // 改变页码
    handleCurrentChange(val) {
      this.currentPage = val
      this.getUserList()
    }
  }
}
</script>

<style scoped>
.user-list {
  padding: 20px;
}
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.user-detail .detail-item {
  margin-bottom: 15px;
}
.user-detail .detail-item label {
  font-weight: bold;
  color: #606266;
}
.danger {
  color: #f56c6c;
}
.success {
  color: #67c23a;
}
</style>
