<template>
  <div class="order-list">
    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用户账号">
          <el-input v-model="searchForm.userId" placeholder="请输入用户账号"></el-input>
        </el-form-item>
        <el-form-item label="电站名称">
          <el-input v-model="searchForm.stationName" placeholder="请输入电站名称"></el-input>
        </el-form-item>
        <el-form-item label="充电号">
          <el-input v-model="searchForm.chargeNo" placeholder="请输入充电号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">确认</el-button>
          <el-button @click="handleReset">清空</el-button>
          <el-button type="primary" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 订单表格 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column prop="chargeNo" label="充电编号" width="180"></el-table-column>
      <el-table-column prop="terminalCode" label="终端编码" width="180"></el-table-column>
      <el-table-column prop="userId" label="充电用户"></el-table-column>
      <el-table-column prop="power" label="电量(度)"></el-table-column>
      <el-table-column prop="amount" label="订单金额(元)"></el-table-column>
      <el-table-column prop="status" label="结算类型"></el-table-column>
      <el-table-column prop="stationName" label="电站名称"></el-table-column>
      <el-table-column prop="settleTime" label="结算时间"></el-table-column>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button type="text" @click="handleRefund(scope.row)">申请退款</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrderList',
  data() {
    return {
      searchForm: {
        dateRange: [],
        userId: '',
        stationName: '',
        chargeNo: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  created() {
    this.getOrderList()
  },
  methods: {
    // 获取订单列表
    getOrderList() {
      // TODO: 调用接口获取订单列表数据
      this.tableData = [
        {
          chargeNo: '174409670417200006',
          terminalCode: '310101110000201',
          userId: '17621755592',
          power: '11.0001',
          amount: '14.52',
          status: '正常结算',
          stationName: '公站TestF1',
          settleTime: '2025-04-11 17:38:08'
        }
        // 更多数据...
      ]
      this.total = 66 // 总条数
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.getOrderList()
    },
    // 重置
    handleReset() {
      this.searchForm = {
        dateRange: [],
        userId: '',
        stationName: '',
        chargeNo: ''
      }
      this.handleSearch()
    },
    // 导出
    handleExport() {
      // TODO: 实现导出功能
    },
    // 查看详情
    handleDetail(row) {
      // TODO: 实现查看详情功能
    },
    // 申请退款
    handleRefund(row) {
      // TODO: 实现申请退款功能
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.pageSize = val
      this.getOrderList()
    },
    // 改变页码
    handleCurrentChange(val) {
      this.currentPage = val
      this.getOrderList()
    }
  }
}
</script>

<style scoped>
.order-list {
  padding: 20px;
}
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 