<template>
  <div class="user-detail">
    <!-- 用户基本信息 -->
    <el-card class="user-info-card">
      <div slot="header" class="card-header">
        <span>用户基本信息</span>
        <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>用户ID：</label>
            <span>{{ userInfo.userId }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>用户姓名：</label>
            <span>{{ userInfo.userName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>手机号：</label>
            <span>{{ userInfo.phone }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>邮箱：</label>
            <span>{{ userInfo.email }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>账户余额：</label>
            <span class="balance">{{ userInfo.balance }}元</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>状态：</label>
            <el-tag :type="userInfo.status === '1' ? 'success' : 'danger'">
              {{ userInfo.status === '1' ? '正常' : '禁用' }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>注册时间：</label>
            <span>{{ userInfo.createTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>最后登录：</label>
            <span>{{ userInfo.lastLoginTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>登录次数：</label>
            <span>{{ userInfo.loginCount }}次</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 账户统计 -->
    <el-card class="stats-card">
      <div slot="header">
        <span>账户统计</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalRecharge }}</div>
            <div class="stat-label">累计充值(元)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalConsume }}</div>
            <div class="stat-label">累计消费(元)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalOrders }}</div>
            <div class="stat-label">订单总数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalPower }}</div>
            <div class="stat-label">累计充电量(度)</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近订单 -->
    <el-card class="orders-card">
      <div slot="header" class="card-header">
        <span>最近订单</span>
        <el-button type="text" @click="viewAllOrders">查看全部</el-button>
      </div>
      <el-table :data="recentOrders" border>
        <el-table-column prop="chargeNo" label="充电编号" width="180"></el-table-column>
        <el-table-column prop="stationName" label="电站名称"></el-table-column>
        <el-table-column prop="power" label="电量(度)" width="100"></el-table-column>
        <el-table-column prop="amount" label="金额(元)" width="100"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
      </el-table>
    </el-card>

    <!-- 余额变动记录 -->
    <el-card class="balance-card">
      <div slot="header" class="card-header">
        <span>余额变动记录</span>
        <el-button type="text" @click="viewAllBalanceRecords">查看全部</el-button>
      </div>
      <el-table :data="balanceRecords" border>
        <el-table-column prop="type" label="变动类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === '充值' ? 'success' : 'warning'">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="变动金额(元)" width="120">
          <template slot-scope="scope">
            <span :class="scope.row.type === '充值' ? 'positive' : 'negative'">
              {{ scope.row.type === '充值' ? '+' : '-' }}{{ scope.row.amount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="余额(元)" width="120"></el-table-column>
        <el-table-column prop="description" label="说明"></el-table-column>
        <el-table-column prop="createTime" label="时间" width="160"></el-table-column>
      </el-table>
    </el-card>

    <!-- 编辑用户弹窗 -->
    <el-dialog title="编辑用户" :visible.sync="editVisible" width="500px">
      <el-form :model="editForm" :rules="formRules" ref="editForm" label-width="80px">
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="editForm.userName" placeholder="请输入用户姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio label="1">正常</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserDetail',
  data() {
    return {
      userId: '',
      userInfo: {},
      stats: {},
      recentOrders: [],
      balanceRecords: [],
      editVisible: false,
      editForm: {},
      formRules: {
        userName: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.userId = this.$route.params.id
    this.getUserDetail()
    this.getStats()
    this.getRecentOrders()
    this.getBalanceRecords()
  },
  methods: {
    // 获取用户详情
    getUserDetail() {
      // TODO: 调用接口获取用户详情
      this.userInfo = {
        userId: 'U001',
        userName: '张三',
        phone: '13800138001',
        email: '<EMAIL>',
        balance: '1250.50',
        status: '1',
        createTime: '2024-01-15 10:30:00',
        lastLoginTime: '2025-01-20 14:25:30',
        loginCount: 156
      }
    },
    // 获取统计数据
    getStats() {
      // TODO: 调用接口获取统计数据
      this.stats = {
        totalRecharge: '5000.00',
        totalConsume: '3749.50',
        totalOrders: 89,
        totalPower: '1250.5'
      }
    },
    // 获取最近订单
    getRecentOrders() {
      // TODO: 调用接口获取最近订单
      this.recentOrders = [
        {
          chargeNo: '174409670417200006',
          stationName: '公站TestF1',
          power: '11.0001',
          amount: '14.52',
          status: '已完成',
          createTime: '2025-01-20 17:38:08'
        },
        {
          chargeNo: '174409670417200005',
          stationName: '公站TestF2',
          power: '8.5',
          amount: '11.20',
          status: '已完成',
          createTime: '2025-01-19 15:20:15'
        }
      ]
    },
    // 获取余额变动记录
    getBalanceRecords() {
      // TODO: 调用接口获取余额变动记录
      this.balanceRecords = [
        {
          type: '充值',
          amount: '100.00',
          balance: '1250.50',
          description: '在线充值',
          createTime: '2025-01-20 10:30:00'
        },
        {
          type: '消费',
          amount: '14.52',
          balance: '1150.50',
          description: '充电消费 - 174409670417200006',
          createTime: '2025-01-20 17:38:08'
        }
      ]
    },
    // 编辑用户
    handleEdit() {
      this.editForm = { ...this.userInfo }
      this.editVisible = true
    },
    // 保存编辑
    handleSave() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          // TODO: 调用接口保存用户信息
          this.userInfo = { ...this.editForm }
          this.editVisible = false
          this.$message.success('保存成功')
        }
      })
    },
    // 查看全部订单
    viewAllOrders() {
      this.$router.push(`/orders?userId=${this.userId}`)
    },
    // 查看全部余额记录
    viewAllBalanceRecords() {
      this.$router.push(`/balance-records?userId=${this.userId}`)
    },
    // 获取订单状态类型
    getOrderStatusType(status) {
      const statusMap = {
        '已完成': 'success',
        '进行中': 'warning',
        '已取消': 'danger'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.user-detail {
  padding: 20px;
}
.user-info-card, .stats-card, .orders-card, .balance-card {
  margin-bottom: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.info-item {
  margin-bottom: 15px;
}
.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}
.balance {
  color: #67c23a;
  font-weight: bold;
}
.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}
.stat-label {
  color: #909399;
  font-size: 14px;
}
.positive {
  color: #67c23a;
}
.negative {
  color: #f56c6c;
}
</style>
