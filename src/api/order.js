/**
 * 订单相关API接口
 */
import { get, post, put, patch, del } from './request.js'

/**
 * 订单管理API
 */
export const orderAPI = {
  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   * @param {string} params.chargeNo 充电编号
   * @param {string} params.userId 用户ID
   * @param {string} params.stationName 电站名称
   * @param {string} params.status 订单状态
   * @param {Array} params.dateRange 日期范围 [startDate, endDate]
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.sortBy 排序字段
   * @param {string} params.sortOrder 排序方向
   * @returns {Promise} 订单列表数据
   */
  getOrderList(params = {}) {
    // 处理日期范围参数
    if (params.dateRange && params.dateRange.length === 2) {
      params.startDate = params.dateRange[0]
      params.endDate = params.dateRange[1]
      delete params.dateRange
    }
    return get('/orders', params)
  },

  /**
   * 根据ID获取订单详情
   * @param {string} orderId 订单ID
   * @returns {Promise} 订单详情数据
   */
  getOrderById(orderId) {
    return get(`/orders/${orderId}`)
  },

  /**
   * 根据充电编号获取订单详情
   * @param {string} chargeNo 充电编号
   * @returns {Promise} 订单详情数据
   */
  getOrderByChargeNo(chargeNo) {
    return get(`/orders/charge/${chargeNo}`)
  },

  /**
   * 创建新订单
   * @param {Object} orderData 订单数据
   * @param {string} orderData.userId 用户ID
   * @param {string} orderData.stationId 电站ID
   * @param {string} orderData.terminalCode 终端编码
   * @param {number} orderData.estimatedAmount 预估金额
   * @returns {Promise} 创建结果
   */
  createOrder(orderData) {
    return post('/orders', orderData)
  },

  /**
   * 更新订单信息
   * @param {string} orderId 订单ID
   * @param {Object} orderData 更新的订单数据
   * @returns {Promise} 更新结果
   */
  updateOrder(orderId, orderData) {
    return put(`/orders/${orderId}`, orderData)
  },

  /**
   * 取消订单
   * @param {string} orderId 订单ID
   * @param {string} reason 取消原因
   * @returns {Promise} 取消结果
   */
  cancelOrder(orderId, reason) {
    return patch(`/orders/${orderId}/cancel`, { reason })
  },

  /**
   * 完成订单
   * @param {string} orderId 订单ID
   * @param {Object} completionData 完成数据
   * @param {number} completionData.actualPower 实际电量
   * @param {number} completionData.actualAmount 实际金额
   * @returns {Promise} 完成结果
   */
  completeOrder(orderId, completionData) {
    return patch(`/orders/${orderId}/complete`, completionData)
  },

  /**
   * 申请退款
   * @param {string} orderId 订单ID
   * @param {Object} refundData 退款数据
   * @param {number} refundData.refundAmount 退款金额
   * @param {string} refundData.reason 退款原因
   * @returns {Promise} 申请结果
   */
  applyRefund(orderId, refundData) {
    return post(`/orders/${orderId}/refund`, refundData)
  },

  /**
   * 处理退款申请
   * @param {string} orderId 订单ID
   * @param {Object} processData 处理数据
   * @param {string} processData.action 处理动作 (approve, reject)
   * @param {string} processData.remark 处理备注
   * @returns {Promise} 处理结果
   */
  processRefund(orderId, processData) {
    return patch(`/orders/${orderId}/refund/process`, processData)
  },

  /**
   * 获取订单统计数据
   * @param {Object} params 查询参数
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   * @param {string} params.groupBy 分组方式 (day, week, month)
   * @returns {Promise} 统计数据
   */
  getOrderStats(params = {}) {
    return get('/orders/stats', params)
  },

  /**
   * 导出订单列表
   * @param {Object} params 查询参数
   * @returns {Promise} 导出结果
   */
  exportOrders(params = {}) {
    return get('/orders/export', params)
  },

  /**
   * 获取用户订单列表
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   * @returns {Promise} 用户订单列表
   */
  getUserOrders(userId, params = {}) {
    return get(`/users/${userId}/orders`, params)
  },

  /**
   * 获取电站订单列表
   * @param {string} stationId 电站ID
   * @param {Object} params 查询参数
   * @returns {Promise} 电站订单列表
   */
  getStationOrders(stationId, params = {}) {
    return get(`/stations/${stationId}/orders`, params)
  }
}

/**
 * 充电会话API
 */
export const chargingAPI = {
  /**
   * 开始充电
   * @param {Object} chargingData 充电数据
   * @param {string} chargingData.userId 用户ID
   * @param {string} chargingData.stationId 电站ID
   * @param {string} chargingData.terminalCode 终端编码
   * @param {string} chargingData.connectorId 连接器ID
   * @returns {Promise} 开始充电结果
   */
  startCharging(chargingData) {
    return post('/charging/start', chargingData)
  },

  /**
   * 停止充电
   * @param {string} sessionId 充电会话ID
   * @returns {Promise} 停止充电结果
   */
  stopCharging(sessionId) {
    return post(`/charging/${sessionId}/stop`)
  },

  /**
   * 获取充电状态
   * @param {string} sessionId 充电会话ID
   * @returns {Promise} 充电状态
   */
  getChargingStatus(sessionId) {
    return get(`/charging/${sessionId}/status`)
  },

  /**
   * 获取充电历史记录
   * @param {string} sessionId 充电会话ID
   * @returns {Promise} 充电历史记录
   */
  getChargingHistory(sessionId) {
    return get(`/charging/${sessionId}/history`)
  },

  /**
   * 获取实时充电数据
   * @param {string} sessionId 充电会话ID
   * @returns {Promise} 实时充电数据
   */
  getRealtimeData(sessionId) {
    return get(`/charging/${sessionId}/realtime`)
  },

  /**
   * 预约充电
   * @param {Object} reservationData 预约数据
   * @param {string} reservationData.userId 用户ID
   * @param {string} reservationData.stationId 电站ID
   * @param {string} reservationData.terminalCode 终端编码
   * @param {string} reservationData.startTime 开始时间
   * @param {number} reservationData.duration 持续时间(分钟)
   * @returns {Promise} 预约结果
   */
  makeReservation(reservationData) {
    return post('/charging/reservation', reservationData)
  },

  /**
   * 取消预约
   * @param {string} reservationId 预约ID
   * @returns {Promise} 取消结果
   */
  cancelReservation(reservationId) {
    return del(`/charging/reservation/${reservationId}`)
  },

  /**
   * 获取用户预约列表
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   * @returns {Promise} 预约列表
   */
  getUserReservations(userId, params = {}) {
    return get(`/users/${userId}/reservations`, params)
  }
}

/**
 * 支付相关API
 */
export const paymentAPI = {
  /**
   * 创建支付订单
   * @param {Object} paymentData 支付数据
   * @param {string} paymentData.orderId 订单ID
   * @param {number} paymentData.amount 支付金额
   * @param {string} paymentData.payMethod 支付方式
   * @returns {Promise} 支付订单
   */
  createPayment(paymentData) {
    return post('/payments', paymentData)
  },

  /**
   * 查询支付状态
   * @param {string} paymentId 支付ID
   * @returns {Promise} 支付状态
   */
  getPaymentStatus(paymentId) {
    return get(`/payments/${paymentId}/status`)
  },

  /**
   * 支付回调处理
   * @param {Object} callbackData 回调数据
   * @returns {Promise} 处理结果
   */
  handlePaymentCallback(callbackData) {
    return post('/payments/callback', callbackData)
  },

  /**
   * 申请退款
   * @param {string} paymentId 支付ID
   * @param {Object} refundData 退款数据
   * @returns {Promise} 退款申请结果
   */
  requestRefund(paymentId, refundData) {
    return post(`/payments/${paymentId}/refund`, refundData)
  },

  /**
   * 查询退款状态
   * @param {string} refundId 退款ID
   * @returns {Promise} 退款状态
   */
  getRefundStatus(refundId) {
    return get(`/refunds/${refundId}/status`)
  },

  /**
   * 获取支付方式列表
   * @returns {Promise} 支付方式列表
   */
  getPaymentMethods() {
    return get('/payments/methods')
  },

  /**
   * 获取用户支付记录
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   * @returns {Promise} 支付记录
   */
  getUserPayments(userId, params = {}) {
    return get(`/users/${userId}/payments`, params)
  }
}

// 默认导出所有API
export default {
  orderAPI,
  chargingAPI,
  paymentAPI
}
