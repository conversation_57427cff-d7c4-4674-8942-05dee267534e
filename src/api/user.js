/**
 * 用户相关API接口
 */
import { get, post, put, patch, del } from './request.js'

/**
 * 用户管理API
 */
export const userAPI = {
  /**
   * 获取用户列表
   * @param {Object} params 查询参数
   * @param {string} params.userId 用户ID
   * @param {string} params.userName 用户姓名
   * @param {string} params.phone 手机号
   * @param {string} params.email 邮箱
   * @param {string} params.status 状态 (1:正常, 0:禁用)
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.sortBy 排序字段
   * @param {string} params.sortOrder 排序方向 (asc, desc)
   * @returns {Promise} 用户列表数据
   */
  getUserList(params = {}) {
    return get('/users', params)
  },

  /**
   * 根据ID获取用户详情
   * @param {string} userId 用户ID
   * @returns {Promise} 用户详情数据
   */
  getUserById(userId) {
    return get(`/users/${userId}`)
  },

  /**
   * 创建新用户
   * @param {Object} userData 用户数据
   * @param {string} userData.userName 用户姓名
   * @param {string} userData.phone 手机号
   * @param {string} userData.email 邮箱
   * @param {string} userData.password 密码
   * @param {number} userData.balance 初始余额
   * @param {string} userData.status 状态
   * @returns {Promise} 创建结果
   */
  createUser(userData) {
    return post('/users', userData)
  },

  /**
   * 更新用户信息
   * @param {string} userId 用户ID
   * @param {Object} userData 更新的用户数据
   * @returns {Promise} 更新结果
   */
  updateUser(userId, userData) {
    return put(`/users/${userId}`, userData)
  },

  /**
   * 部分更新用户信息
   * @param {string} userId 用户ID
   * @param {Object} userData 部分用户数据
   * @returns {Promise} 更新结果
   */
  patchUser(userId, userData) {
    return patch(`/users/${userId}`, userData)
  },

  /**
   * 删除用户
   * @param {string} userId 用户ID
   * @returns {Promise} 删除结果
   */
  deleteUser(userId) {
    return del(`/users/${userId}`)
  },

  /**
   * 批量删除用户
   * @param {Array} userIds 用户ID数组
   * @returns {Promise} 删除结果
   */
  batchDeleteUsers(userIds) {
    return post('/users/batch-delete', { userIds })
  },

  /**
   * 切换用户状态
   * @param {string} userId 用户ID
   * @param {string} status 新状态
   * @returns {Promise} 更新结果
   */
  toggleUserStatus(userId, status) {
    return patch(`/users/${userId}/status`, { status })
  },

  /**
   * 重置用户密码
   * @param {string} userId 用户ID
   * @param {string} newPassword 新密码
   * @returns {Promise} 重置结果
   */
  resetPassword(userId, newPassword) {
    return patch(`/users/${userId}/password`, { password: newPassword })
  },

  /**
   * 获取用户统计信息
   * @param {string} userId 用户ID
   * @returns {Promise} 统计数据
   */
  getUserStats(userId) {
    return get(`/users/${userId}/stats`)
  },

  /**
   * 获取用户最近订单
   * @param {string} userId 用户ID
   * @param {number} limit 限制数量
   * @returns {Promise} 订单列表
   */
  getUserRecentOrders(userId, limit = 10) {
    return get(`/users/${userId}/orders/recent`, { limit })
  },

  /**
   * 获取用户余额变动记录
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   * @returns {Promise} 余额记录
   */
  getUserBalanceRecords(userId, params = {}) {
    return get(`/users/${userId}/balance-records`, params)
  },

  /**
   * 用户充值
   * @param {string} userId 用户ID
   * @param {Object} rechargeData 充值数据
   * @param {number} rechargeData.amount 充值金额
   * @param {string} rechargeData.payMethod 支付方式
   * @returns {Promise} 充值结果
   */
  rechargeBalance(userId, rechargeData) {
    return post(`/users/${userId}/recharge`, rechargeData)
  },

  /**
   * 用户余额扣减
   * @param {string} userId 用户ID
   * @param {Object} deductData 扣减数据
   * @param {number} deductData.amount 扣减金额
   * @param {string} deductData.reason 扣减原因
   * @returns {Promise} 扣减结果
   */
  deductBalance(userId, deductData) {
    return post(`/users/${userId}/deduct`, deductData)
  },

  /**
   * 导出用户列表
   * @param {Object} params 查询参数
   * @returns {Promise} 导出结果
   */
  exportUsers(params = {}) {
    return get('/users/export', params)
  },

  /**
   * 批量导入用户
   * @param {FormData} formData 包含Excel文件的表单数据
   * @returns {Promise} 导入结果
   */
  importUsers(formData) {
    return post('/users/import', formData, {
      headers: {
        // 让浏览器自动设置Content-Type为multipart/form-data
      }
    })
  }
}

/**
 * 用户认证API
 */
export const authAPI = {
  /**
   * 用户登录
   * @param {Object} loginData 登录数据
   * @param {string} loginData.username 用户名/手机号/邮箱
   * @param {string} loginData.password 密码
   * @param {string} loginData.captcha 验证码
   * @returns {Promise} 登录结果
   */
  login(loginData) {
    return post('/auth/login', loginData)
  },

  /**
   * 用户注册
   * @param {Object} registerData 注册数据
   * @returns {Promise} 注册结果
   */
  register(registerData) {
    return post('/auth/register', registerData)
  },

  /**
   * 用户登出
   * @returns {Promise} 登出结果
   */
  logout() {
    return post('/auth/logout')
  },

  /**
   * 刷新Token
   * @param {string} refreshToken 刷新令牌
   * @returns {Promise} 新的Token
   */
  refreshToken(refreshToken) {
    return post('/auth/refresh', { refreshToken })
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} 用户信息
   */
  getCurrentUser() {
    return get('/auth/me')
  },

  /**
   * 更新当前用户信息
   * @param {Object} userData 用户数据
   * @returns {Promise} 更新结果
   */
  updateCurrentUser(userData) {
    return put('/auth/me', userData)
  },

  /**
   * 修改当前用户密码
   * @param {Object} passwordData 密码数据
   * @param {string} passwordData.oldPassword 旧密码
   * @param {string} passwordData.newPassword 新密码
   * @returns {Promise} 修改结果
   */
  changePassword(passwordData) {
    return post('/auth/change-password', passwordData)
  },

  /**
   * 发送验证码
   * @param {Object} data 验证码数据
   * @param {string} data.phone 手机号
   * @param {string} data.type 验证码类型 (register, login, reset)
   * @returns {Promise} 发送结果
   */
  sendVerificationCode(data) {
    return post('/auth/send-code', data)
  },

  /**
   * 验证验证码
   * @param {Object} data 验证数据
   * @param {string} data.phone 手机号
   * @param {string} data.code 验证码
   * @param {string} data.type 验证码类型
   * @returns {Promise} 验证结果
   */
  verifyCode(data) {
    return post('/auth/verify-code', data)
  },

  /**
   * 忘记密码
   * @param {Object} data 重置数据
   * @param {string} data.phone 手机号
   * @param {string} data.code 验证码
   * @param {string} data.newPassword 新密码
   * @returns {Promise} 重置结果
   */
  forgotPassword(data) {
    return post('/auth/forgot-password', data)
  }
}

/**
 * 用户偏好设置API
 */
export const userPreferenceAPI = {
  /**
   * 获取用户偏好设置
   * @param {string} userId 用户ID
   * @returns {Promise} 偏好设置
   */
  getPreferences(userId) {
    return get(`/users/${userId}/preferences`)
  },

  /**
   * 更新用户偏好设置
   * @param {string} userId 用户ID
   * @param {Object} preferences 偏好设置
   * @returns {Promise} 更新结果
   */
  updatePreferences(userId, preferences) {
    return put(`/users/${userId}/preferences`, preferences)
  },

  /**
   * 获取用户通知设置
   * @param {string} userId 用户ID
   * @returns {Promise} 通知设置
   */
  getNotificationSettings(userId) {
    return get(`/users/${userId}/notifications`)
  },

  /**
   * 更新用户通知设置
   * @param {string} userId 用户ID
   * @param {Object} settings 通知设置
   * @returns {Promise} 更新结果
   */
  updateNotificationSettings(userId, settings) {
    return put(`/users/${userId}/notifications`, settings)
  }
}

// 默认导出所有API
export default {
  userAPI,
  authAPI,
  userPreferenceAPI
}
