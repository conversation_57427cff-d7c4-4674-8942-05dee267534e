/**
 * HTTP请求工具类
 * 基于fetch API封装，支持请求拦截、响应拦截、错误处理等
 */

// API基础配置
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
}

/**
 * 请求拦截器
 * @param {Object} config 请求配置
 * @returns {Object} 处理后的请求配置
 */
function requestInterceptor(config) {
  // 添加认证token
  const token = localStorage.getItem('token')
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    }
  }

  // 添加请求时间戳
  config.headers['X-Request-Time'] = Date.now()

  console.log('Request:', config)
  return config
}

/**
 * 响应拦截器
 * @param {Response} response 响应对象
 * @returns {Promise} 处理后的响应数据
 */
async function responseInterceptor(response) {
  const data = await response.json()
  
  console.log('Response:', data)

  // 统一的响应格式处理
  if (response.ok) {
    return {
      success: true,
      data: data.data || data,
      message: data.message || 'Success',
      code: data.code || 200
    }
  } else {
    // 处理HTTP错误状态
    throw new Error(data.message || `HTTP Error: ${response.status}`)
  }
}

/**
 * 错误处理器
 * @param {Error} error 错误对象
 */
function errorHandler(error) {
  console.error('API Error:', error)
  
  // 根据错误类型进行不同处理
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    throw new Error('网络连接失败，请检查网络设置')
  }
  
  if (error.message.includes('401')) {
    // 未授权，清除token并跳转登录
    localStorage.removeItem('token')
    window.location.href = '/login'
    throw new Error('登录已过期，请重新登录')
  }
  
  if (error.message.includes('403')) {
    throw new Error('没有权限访问该资源')
  }
  
  if (error.message.includes('404')) {
    throw new Error('请求的资源不存在')
  }
  
  if (error.message.includes('500')) {
    throw new Error('服务器内部错误，请稍后重试')
  }
  
  throw error
}

/**
 * 创建请求实例
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
async function createRequest(url, options = {}) {
  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${API_CONFIG.baseURL}${url}`
  
  // 合并默认配置
  const config = {
    method: 'GET',
    headers: { ...API_CONFIG.headers },
    ...options
  }
  
  // 应用请求拦截器
  const interceptedConfig = requestInterceptor(config)
  
  try {
    // 创建AbortController用于超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout)
    
    // 发送请求
    const response = await fetch(fullUrl, {
      ...interceptedConfig,
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    // 应用响应拦截器
    return await responseInterceptor(response)
    
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请稍后重试')
    }
    errorHandler(error)
  }
}

/**
 * GET请求
 * @param {string} url 请求URL
 * @param {Object} params 查询参数
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
export function get(url, params = {}, options = {}) {
  // 构建查询字符串
  const queryString = new URLSearchParams(params).toString()
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return createRequest(fullUrl, {
    method: 'GET',
    ...options
  })
}

/**
 * POST请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
export function post(url, data = {}, options = {}) {
  return createRequest(url, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options
  })
}

/**
 * PUT请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
export function put(url, data = {}, options = {}) {
  return createRequest(url, {
    method: 'PUT',
    body: JSON.stringify(data),
    ...options
  })
}

/**
 * PATCH请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
export function patch(url, data = {}, options = {}) {
  return createRequest(url, {
    method: 'PATCH',
    body: JSON.stringify(data),
    ...options
  })
}

/**
 * DELETE请求
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
export function del(url, options = {}) {
  return createRequest(url, {
    method: 'DELETE',
    ...options
  })
}

/**
 * 文件上传请求
 * @param {string} url 请求URL
 * @param {FormData} formData 表单数据
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
export function upload(url, formData, options = {}) {
  return createRequest(url, {
    method: 'POST',
    body: formData,
    headers: {
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
    },
    ...options
  })
}

/**
 * 下载文件
 * @param {string} url 下载URL
 * @param {string} filename 文件名
 * @param {Object} options 请求选项
 */
export async function download(url, filename, options = {}) {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      ...options
    })
    
    if (!response.ok) {
      throw new Error('下载失败')
    }
    
    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
  } catch (error) {
    console.error('Download error:', error)
    throw new Error('文件下载失败')
  }
}

// 默认导出请求方法
export default {
  get,
  post,
  put,
  patch,
  delete: del,
  upload,
  download
}
