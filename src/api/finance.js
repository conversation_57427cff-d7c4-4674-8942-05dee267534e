/**
 * 财务相关API接口
 */
import { get, post, put, patch, del } from './request.js'

/**
 * 财务管理API
 */
export const financeAPI = {
  /**
   * 获取财务概览
   * @param {Object} params 查询参数
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   * @param {string} params.operatorId 运营商ID
   * @returns {Promise} 财务概览数据
   */
  getFinanceOverview(params = {}) {
    return get('/finance/overview', params)
  },

  /**
   * 获取收入统计
   * @param {Object} params 查询参数
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   * @param {string} params.groupBy 分组方式 (day, week, month, year)
   * @param {string} params.operatorId 运营商ID
   * @returns {Promise} 收入统计数据
   */
  getRevenueStats(params = {}) {
    return get('/finance/revenue', params)
  },

  /**
   * 获取支出统计
   * @param {Object} params 查询参数
   * @returns {Promise} 支出统计数据
   */
  getExpenseStats(params = {}) {
    return get('/finance/expense', params)
  },

  /**
   * 获取利润分析
   * @param {Object} params 查询参数
   * @returns {Promise} 利润分析数据
   */
  getProfitAnalysis(params = {}) {
    return get('/finance/profit', params)
  },

  /**
   * 获取电站收益排行
   * @param {Object} params 查询参数
   * @param {number} params.limit 限制数量
   * @returns {Promise} 电站收益排行
   */
  getStationRevenueRanking(params = {}) {
    return get('/finance/station-ranking', params)
  },

  /**
   * 获取用户消费排行
   * @param {Object} params 查询参数
   * @param {number} params.limit 限制数量
   * @returns {Promise} 用户消费排行
   */
  getUserConsumptionRanking(params = {}) {
    return get('/finance/user-ranking', params)
  }
}

/**
 * 账单管理API
 */
export const billAPI = {
  /**
   * 获取账单列表
   * @param {Object} params 查询参数
   * @param {string} params.billNo 账单号
   * @param {string} params.userId 用户ID
   * @param {string} params.operatorId 运营商ID
   * @param {string} params.status 账单状态
   * @param {string} params.billType 账单类型
   * @param {Array} params.dateRange 日期范围
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise} 账单列表
   */
  getBillList(params = {}) {
    if (params.dateRange && params.dateRange.length === 2) {
      params.startDate = params.dateRange[0]
      params.endDate = params.dateRange[1]
      delete params.dateRange
    }
    return get('/bills', params)
  },

  /**
   * 获取账单详情
   * @param {string} billId 账单ID
   * @returns {Promise} 账单详情
   */
  getBillById(billId) {
    return get(`/bills/${billId}`)
  },

  /**
   * 创建账单
   * @param {Object} billData 账单数据
   * @returns {Promise} 创建结果
   */
  createBill(billData) {
    return post('/bills', billData)
  },

  /**
   * 更新账单
   * @param {string} billId 账单ID
   * @param {Object} billData 更新数据
   * @returns {Promise} 更新结果
   */
  updateBill(billId, billData) {
    return put(`/bills/${billId}`, billData)
  },

  /**
   * 删除账单
   * @param {string} billId 账单ID
   * @returns {Promise} 删除结果
   */
  deleteBill(billId) {
    return del(`/bills/${billId}`)
  },

  /**
   * 支付账单
   * @param {string} billId 账单ID
   * @param {Object} paymentData 支付数据
   * @returns {Promise} 支付结果
   */
  payBill(billId, paymentData) {
    return post(`/bills/${billId}/pay`, paymentData)
  },

  /**
   * 取消账单
   * @param {string} billId 账单ID
   * @param {string} reason 取消原因
   * @returns {Promise} 取消结果
   */
  cancelBill(billId, reason) {
    return patch(`/bills/${billId}/cancel`, { reason })
  },

  /**
   * 导出账单
   * @param {Object} params 查询参数
   * @returns {Promise} 导出结果
   */
  exportBills(params = {}) {
    return get('/bills/export', params)
  },

  /**
   * 获取用户账单
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   * @returns {Promise} 用户账单列表
   */
  getUserBills(userId, params = {}) {
    return get(`/users/${userId}/bills`, params)
  },

  /**
   * 获取运营商账单
   * @param {string} operatorId 运营商ID
   * @param {Object} params 查询参数
   * @returns {Promise} 运营商账单列表
   */
  getOperatorBills(operatorId, params = {}) {
    return get(`/operators/${operatorId}/bills`, params)
  }
}

/**
 * 结算管理API
 */
export const settlementAPI = {
  /**
   * 获取结算列表
   * @param {Object} params 查询参数
   * @returns {Promise} 结算列表
   */
  getSettlementList(params = {}) {
    return get('/settlements', params)
  },

  /**
   * 获取结算详情
   * @param {string} settlementId 结算ID
   * @returns {Promise} 结算详情
   */
  getSettlementById(settlementId) {
    return get(`/settlements/${settlementId}`)
  },

  /**
   * 创建结算
   * @param {Object} settlementData 结算数据
   * @returns {Promise} 创建结果
   */
  createSettlement(settlementData) {
    return post('/settlements', settlementData)
  },

  /**
   * 确认结算
   * @param {string} settlementId 结算ID
   * @returns {Promise} 确认结果
   */
  confirmSettlement(settlementId) {
    return patch(`/settlements/${settlementId}/confirm`)
  },

  /**
   * 取消结算
   * @param {string} settlementId 结算ID
   * @param {string} reason 取消原因
   * @returns {Promise} 取消结果
   */
  cancelSettlement(settlementId, reason) {
    return patch(`/settlements/${settlementId}/cancel`, { reason })
  },

  /**
   * 获取待结算订单
   * @param {Object} params 查询参数
   * @returns {Promise} 待结算订单列表
   */
  getPendingOrders(params = {}) {
    return get('/settlements/pending-orders', params)
  },

  /**
   * 批量结算
   * @param {Object} batchData 批量结算数据
   * @param {Array} batchData.orderIds 订单ID列表
   * @param {string} batchData.operatorId 运营商ID
   * @returns {Promise} 批量结算结果
   */
  batchSettlement(batchData) {
    return post('/settlements/batch', batchData)
  }
}

/**
 * 余额管理API
 */
export const balanceAPI = {
  /**
   * 获取余额记录列表
   * @param {Object} params 查询参数
   * @param {string} params.userId 用户ID
   * @param {string} params.type 变动类型
   * @param {Array} params.dateRange 日期范围
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise} 余额记录列表
   */
  getBalanceRecords(params = {}) {
    if (params.dateRange && params.dateRange.length === 2) {
      params.startDate = params.dateRange[0]
      params.endDate = params.dateRange[1]
      delete params.dateRange
    }
    return get('/balance-records', params)
  },

  /**
   * 获取用户余额记录
   * @param {string} userId 用户ID
   * @param {Object} params 查询参数
   * @returns {Promise} 用户余额记录
   */
  getUserBalanceRecords(userId, params = {}) {
    return get(`/users/${userId}/balance-records`, params)
  },

  /**
   * 创建余额记录
   * @param {Object} recordData 记录数据
   * @param {string} recordData.userId 用户ID
   * @param {string} recordData.type 变动类型 (recharge, consume, refund, adjust)
   * @param {number} recordData.amount 变动金额
   * @param {string} recordData.description 描述
   * @param {string} recordData.relatedId 关联ID
   * @returns {Promise} 创建结果
   */
  createBalanceRecord(recordData) {
    return post('/balance-records', recordData)
  },

  /**
   * 用户充值
   * @param {string} userId 用户ID
   * @param {Object} rechargeData 充值数据
   * @param {number} rechargeData.amount 充值金额
   * @param {string} rechargeData.payMethod 支付方式
   * @param {string} rechargeData.description 描述
   * @returns {Promise} 充值结果
   */
  rechargeBalance(userId, rechargeData) {
    return post(`/users/${userId}/recharge`, rechargeData)
  },

  /**
   * 余额扣减
   * @param {string} userId 用户ID
   * @param {Object} deductData 扣减数据
   * @param {number} deductData.amount 扣减金额
   * @param {string} deductData.reason 扣减原因
   * @param {string} deductData.orderId 关联订单ID
   * @returns {Promise} 扣减结果
   */
  deductBalance(userId, deductData) {
    return post(`/users/${userId}/deduct`, deductData)
  },

  /**
   * 余额退款
   * @param {string} userId 用户ID
   * @param {Object} refundData 退款数据
   * @param {number} refundData.amount 退款金额
   * @param {string} refundData.reason 退款原因
   * @param {string} refundData.orderId 关联订单ID
   * @returns {Promise} 退款结果
   */
  refundBalance(userId, refundData) {
    return post(`/users/${userId}/refund`, refundData)
  },

  /**
   * 余额调整
   * @param {string} userId 用户ID
   * @param {Object} adjustData 调整数据
   * @param {number} adjustData.amount 调整金额 (正数为增加，负数为减少)
   * @param {string} adjustData.reason 调整原因
   * @param {string} adjustData.operatorId 操作员ID
   * @returns {Promise} 调整结果
   */
  adjustBalance(userId, adjustData) {
    return post(`/users/${userId}/adjust`, adjustData)
  },

  /**
   * 获取余额统计
   * @param {Object} params 查询参数
   * @returns {Promise} 余额统计数据
   */
  getBalanceStats(params = {}) {
    return get('/balance-records/stats', params)
  },

  /**
   * 导出余额记录
   * @param {Object} params 查询参数
   * @returns {Promise} 导出结果
   */
  exportBalanceRecords(params = {}) {
    return get('/balance-records/export', params)
  }
}

/**
 * 发票管理API
 */
export const invoiceAPI = {
  /**
   * 获取发票列表
   * @param {Object} params 查询参数
   * @returns {Promise} 发票列表
   */
  getInvoiceList(params = {}) {
    return get('/invoices', params)
  },

  /**
   * 获取发票详情
   * @param {string} invoiceId 发票ID
   * @returns {Promise} 发票详情
   */
  getInvoiceById(invoiceId) {
    return get(`/invoices/${invoiceId}`)
  },

  /**
   * 申请发票
   * @param {Object} invoiceData 发票数据
   * @returns {Promise} 申请结果
   */
  applyInvoice(invoiceData) {
    return post('/invoices', invoiceData)
  },

  /**
   * 审核发票
   * @param {string} invoiceId 发票ID
   * @param {Object} auditData 审核数据
   * @returns {Promise} 审核结果
   */
  auditInvoice(invoiceId, auditData) {
    return patch(`/invoices/${invoiceId}/audit`, auditData)
  },

  /**
   * 开具发票
   * @param {string} invoiceId 发票ID
   * @returns {Promise} 开具结果
   */
  issueInvoice(invoiceId) {
    return patch(`/invoices/${invoiceId}/issue`)
  },

  /**
   * 取消发票
   * @param {string} invoiceId 发票ID
   * @param {string} reason 取消原因
   * @returns {Promise} 取消结果
   */
  cancelInvoice(invoiceId, reason) {
    return patch(`/invoices/${invoiceId}/cancel`, { reason })
  },

  /**
   * 下载发票
   * @param {string} invoiceId 发票ID
   * @returns {Promise} 下载结果
   */
  downloadInvoice(invoiceId) {
    return get(`/invoices/${invoiceId}/download`)
  }
}

// 默认导出所有API
export default {
  financeAPI,
  billAPI,
  settlementAPI,
  balanceAPI,
  invoiceAPI
}
