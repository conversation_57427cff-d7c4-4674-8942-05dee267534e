/**
 * 电站相关API接口
 */
import { get, post, put, patch, del } from './request.js'

/**
 * 电站管理API
 */
export const stationAPI = {
  /**
   * 获取电站列表
   * @param {Object} params 查询参数
   * @param {string} params.stationName 电站名称
   * @param {string} params.city 城市
   * @param {string} params.district 区域
   * @param {string} params.status 状态
   * @param {string} params.operatorId 运营商ID
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {number} params.latitude 纬度
   * @param {number} params.longitude 经度
   * @param {number} params.radius 搜索半径(km)
   * @returns {Promise} 电站列表数据
   */
  getStationList(params = {}) {
    return get('/stations', params)
  },

  /**
   * 根据ID获取电站详情
   * @param {string} stationId 电站ID
   * @returns {Promise} 电站详情数据
   */
  getStationById(stationId) {
    return get(`/stations/${stationId}`)
  },

  /**
   * 创建新电站
   * @param {Object} stationData 电站数据
   * @param {string} stationData.stationName 电站名称
   * @param {string} stationData.address 地址
   * @param {number} stationData.latitude 纬度
   * @param {number} stationData.longitude 经度
   * @param {string} stationData.operatorId 运营商ID
   * @param {Array} stationData.terminals 终端列表
   * @returns {Promise} 创建结果
   */
  createStation(stationData) {
    return post('/stations', stationData)
  },

  /**
   * 更新电站信息
   * @param {string} stationId 电站ID
   * @param {Object} stationData 更新的电站数据
   * @returns {Promise} 更新结果
   */
  updateStation(stationId, stationData) {
    return put(`/stations/${stationId}`, stationData)
  },

  /**
   * 删除电站
   * @param {string} stationId 电站ID
   * @returns {Promise} 删除结果
   */
  deleteStation(stationId) {
    return del(`/stations/${stationId}`)
  },

  /**
   * 获取附近电站
   * @param {Object} location 位置信息
   * @param {number} location.latitude 纬度
   * @param {number} location.longitude 经度
   * @param {number} location.radius 搜索半径(km)
   * @param {number} location.limit 限制数量
   * @returns {Promise} 附近电站列表
   */
  getNearbyStations(location) {
    return get('/stations/nearby', location)
  },

  /**
   * 获取电站统计信息
   * @param {string} stationId 电站ID
   * @param {Object} params 查询参数
   * @returns {Promise} 统计信息
   */
  getStationStats(stationId, params = {}) {
    return get(`/stations/${stationId}/stats`, params)
  },

  /**
   * 获取电站实时状态
   * @param {string} stationId 电站ID
   * @returns {Promise} 实时状态
   */
  getStationRealtime(stationId) {
    return get(`/stations/${stationId}/realtime`)
  },

  /**
   * 切换电站状态
   * @param {string} stationId 电站ID
   * @param {string} status 新状态
   * @returns {Promise} 更新结果
   */
  toggleStationStatus(stationId, status) {
    return patch(`/stations/${stationId}/status`, { status })
  }
}

/**
 * 充电终端API
 */
export const terminalAPI = {
  /**
   * 获取终端列表
   * @param {Object} params 查询参数
   * @returns {Promise} 终端列表
   */
  getTerminalList(params = {}) {
    return get('/terminals', params)
  },

  /**
   * 根据ID获取终端详情
   * @param {string} terminalId 终端ID
   * @returns {Promise} 终端详情
   */
  getTerminalById(terminalId) {
    return get(`/terminals/${terminalId}`)
  },

  /**
   * 获取电站的终端列表
   * @param {string} stationId 电站ID
   * @returns {Promise} 终端列表
   */
  getStationTerminals(stationId) {
    return get(`/stations/${stationId}/terminals`)
  },

  /**
   * 创建新终端
   * @param {Object} terminalData 终端数据
   * @returns {Promise} 创建结果
   */
  createTerminal(terminalData) {
    return post('/terminals', terminalData)
  },

  /**
   * 更新终端信息
   * @param {string} terminalId 终端ID
   * @param {Object} terminalData 更新数据
   * @returns {Promise} 更新结果
   */
  updateTerminal(terminalId, terminalData) {
    return put(`/terminals/${terminalId}`, terminalData)
  },

  /**
   * 删除终端
   * @param {string} terminalId 终端ID
   * @returns {Promise} 删除结果
   */
  deleteTerminal(terminalId) {
    return del(`/terminals/${terminalId}`)
  },

  /**
   * 获取终端实时状态
   * @param {string} terminalId 终端ID
   * @returns {Promise} 实时状态
   */
  getTerminalRealtime(terminalId) {
    return get(`/terminals/${terminalId}/realtime`)
  },

  /**
   * 控制终端
   * @param {string} terminalId 终端ID
   * @param {Object} command 控制命令
   * @param {string} command.action 动作 (start, stop, reset, unlock)
   * @param {Object} command.params 参数
   * @returns {Promise} 控制结果
   */
  controlTerminal(terminalId, command) {
    return post(`/terminals/${terminalId}/control`, command)
  },

  /**
   * 获取终端历史数据
   * @param {string} terminalId 终端ID
   * @param {Object} params 查询参数
   * @returns {Promise} 历史数据
   */
  getTerminalHistory(terminalId, params = {}) {
    return get(`/terminals/${terminalId}/history`, params)
  },

  /**
   * 获取终端故障记录
   * @param {string} terminalId 终端ID
   * @param {Object} params 查询参数
   * @returns {Promise} 故障记录
   */
  getTerminalFaults(terminalId, params = {}) {
    return get(`/terminals/${terminalId}/faults`, params)
  },

  /**
   * 上报终端故障
   * @param {string} terminalId 终端ID
   * @param {Object} faultData 故障数据
   * @returns {Promise} 上报结果
   */
  reportTerminalFault(terminalId, faultData) {
    return post(`/terminals/${terminalId}/faults`, faultData)
  },

  /**
   * 处理终端故障
   * @param {string} faultId 故障ID
   * @param {Object} processData 处理数据
   * @returns {Promise} 处理结果
   */
  processTerminalFault(faultId, processData) {
    return patch(`/faults/${faultId}/process`, processData)
  }
}

/**
 * 连接器API
 */
export const connectorAPI = {
  /**
   * 获取连接器列表
   * @param {string} terminalId 终端ID
   * @returns {Promise} 连接器列表
   */
  getConnectorList(terminalId) {
    return get(`/terminals/${terminalId}/connectors`)
  },

  /**
   * 获取连接器详情
   * @param {string} connectorId 连接器ID
   * @returns {Promise} 连接器详情
   */
  getConnectorById(connectorId) {
    return get(`/connectors/${connectorId}`)
  },

  /**
   * 获取连接器实时状态
   * @param {string} connectorId 连接器ID
   * @returns {Promise} 实时状态
   */
  getConnectorRealtime(connectorId) {
    return get(`/connectors/${connectorId}/realtime`)
  },

  /**
   * 控制连接器
   * @param {string} connectorId 连接器ID
   * @param {Object} command 控制命令
   * @returns {Promise} 控制结果
   */
  controlConnector(connectorId, command) {
    return post(`/connectors/${connectorId}/control`, command)
  },

  /**
   * 预约连接器
   * @param {string} connectorId 连接器ID
   * @param {Object} reservationData 预约数据
   * @returns {Promise} 预约结果
   */
  reserveConnector(connectorId, reservationData) {
    return post(`/connectors/${connectorId}/reserve`, reservationData)
  },

  /**
   * 取消连接器预约
   * @param {string} connectorId 连接器ID
   * @param {string} reservationId 预约ID
   * @returns {Promise} 取消结果
   */
  cancelConnectorReservation(connectorId, reservationId) {
    return del(`/connectors/${connectorId}/reservations/${reservationId}`)
  }
}

/**
 * 运营商API
 */
export const operatorAPI = {
  /**
   * 获取运营商列表
   * @param {Object} params 查询参数
   * @returns {Promise} 运营商列表
   */
  getOperatorList(params = {}) {
    return get('/operators', params)
  },

  /**
   * 获取运营商详情
   * @param {string} operatorId 运营商ID
   * @returns {Promise} 运营商详情
   */
  getOperatorById(operatorId) {
    return get(`/operators/${operatorId}`)
  },

  /**
   * 创建运营商
   * @param {Object} operatorData 运营商数据
   * @returns {Promise} 创建结果
   */
  createOperator(operatorData) {
    return post('/operators', operatorData)
  },

  /**
   * 更新运营商信息
   * @param {string} operatorId 运营商ID
   * @param {Object} operatorData 更新数据
   * @returns {Promise} 更新结果
   */
  updateOperator(operatorId, operatorData) {
    return put(`/operators/${operatorId}`, operatorData)
  },

  /**
   * 删除运营商
   * @param {string} operatorId 运营商ID
   * @returns {Promise} 删除结果
   */
  deleteOperator(operatorId) {
    return del(`/operators/${operatorId}`)
  },

  /**
   * 获取运营商统计信息
   * @param {string} operatorId 运营商ID
   * @param {Object} params 查询参数
   * @returns {Promise} 统计信息
   */
  getOperatorStats(operatorId, params = {}) {
    return get(`/operators/${operatorId}/stats`, params)
  },

  /**
   * 获取运营商电站列表
   * @param {string} operatorId 运营商ID
   * @param {Object} params 查询参数
   * @returns {Promise} 电站列表
   */
  getOperatorStations(operatorId, params = {}) {
    return get(`/operators/${operatorId}/stations`, params)
  }
}

// 默认导出所有API
export default {
  stationAPI,
  terminalAPI,
  connectorAPI,
  operatorAPI
}
