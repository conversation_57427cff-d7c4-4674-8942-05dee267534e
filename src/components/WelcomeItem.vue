<template>
  <div class="order">
    <h1>订单列表</h1>
    <table>
      <thead>
        <tr>
          <th>充电单号</th>
          <th>终端编码</th>
          <th>充电用户</th>
          <th>电量(度)</th>
          <th>订单金额(元)</th>
          <th>计算类型</th>
          <th>电站名称</th>
          <th>结束时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <!-- 这里可以使用 v-for 循环来渲染订单数据 -->
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'Order',
  data() {
    return {
      // 这里可以定义订单数据
    };
  },
  methods: {
    // 这里可以定义处理订单的方法
  },
};
</script>

<style scoped>
.order {
  padding: 20px;
}
table {
  width: 100%;
  border-collapse: collapse;
}
th, td {
  border: 1px solid #ddd;
  padding: 8px;
}
</style>
