import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/order',
      name: 'OrderList',
      component: () => import('../views/OrderList.vue')
    },
    {
      path: '/finance',
      name: 'FinanceView',
      component: () => import('../views/FinanceView.vue')
    },
    {
      path: '/users',
      name: 'UserList',
      component: () => import('../views/UserList.vue')
    },
    {
      path: '/users/:id',
      name: 'UserDetail',
      component: () => import('../views/UserDetail.vue')
    },
    {
      path: '/profile',
      name: 'UserProfile',
      component: () => import('../views/UserProfile.vue')
    }
  ],
})

export default router