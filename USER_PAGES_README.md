# Vue用户管理页面

本项目新增了完整的用户管理功能，包含以下页面：

## 页面列表

### 1. 用户列表页面 (`/users`)
**文件位置**: `src/views/UserList.vue`

**功能特性**:
- 用户搜索：支持按用户账号、姓名、手机号、状态搜索
- 用户列表：展示用户基本信息（ID、姓名、手机号、邮箱、余额、状态、注册时间、最后登录时间）
- 用户操作：
  - 查看详情：跳转到用户详情页面
  - 编辑用户：弹窗编辑用户基本信息
  - 启用/禁用：切换用户状态
  - 删除用户：删除用户（需确认）
- 新增用户：弹窗表单新增用户
- 分页功能：支持分页显示和每页数量设置

**主要组件**:
- Element UI 表格组件
- 搜索表单
- 编辑/新增弹窗
- 分页组件

### 2. 用户详情页面 (`/users/:id`)
**文件位置**: `src/views/UserDetail.vue`

**功能特性**:
- 用户基本信息：完整展示用户详细信息
- 账户统计：显示累计充值、消费、订单数、充电量等统计数据
- 最近订单：展示用户最近的充电订单
- 余额变动记录：显示用户账户余额变动历史
- 编辑功能：支持编辑用户基本信息

**数据展示**:
- 卡片式布局
- 统计数据可视化
- 表格展示订单和余额记录
- 状态标签显示

### 3. 用户个人资料页面 (`/profile`)
**文件位置**: `src/views/UserProfile.vue`

**功能特性**:
- 个人信息管理：查看和编辑个人基本信息
- 密码修改：安全的密码修改功能
- 账户信息：显示账户余额和统计信息
- 账户充值：支持多种支付方式充值
- 快捷操作：快速访问常用功能

**布局特点**:
- 左右分栏布局
- 响应式设计
- 卡片式组件
- 表单验证

## 技术特性

### 前端技术栈
- **Vue 3**: 使用Composition API
- **Vue Router**: 路由管理
- **Element UI**: UI组件库
- **CSS**: 响应式布局和样式

### 组件特性
- **表单验证**: 完整的表单验证规则
- **状态管理**: 本地状态管理
- **路由导航**: 页面间跳转
- **弹窗组件**: 模态框交互
- **分页组件**: 数据分页显示

### 数据结构

#### 用户信息结构
```javascript
{
  userId: 'U001',           // 用户ID
  userName: '张三',         // 用户姓名
  phone: '13800138001',     // 手机号
  email: '<EMAIL>', // 邮箱
  balance: '1250.50',       // 账户余额
  status: '1',              // 状态 (1:正常, 0:禁用)
  createTime: '2024-01-15 10:30:00',    // 注册时间
  lastLoginTime: '2025-01-20 14:25:30', // 最后登录时间
  loginCount: 156           // 登录次数
}
```

#### 订单信息结构
```javascript
{
  chargeNo: '174409670417200006',      // 充电编号
  stationName: '公站TestF1',           // 电站名称
  power: '11.0001',                    // 电量(度)
  amount: '14.52',                     // 金额(元)
  status: '已完成',                    // 状态
  createTime: '2025-01-20 17:38:08'    // 创建时间
}
```

## 路由配置

```javascript
// 用户管理相关路由
{
  path: '/users',
  name: 'UserList',
  component: () => import('../views/UserList.vue')
},
{
  path: '/users/:id',
  name: 'UserDetail', 
  component: () => import('../views/UserDetail.vue')
},
{
  path: '/profile',
  name: 'UserProfile',
  component: () => import('../views/UserProfile.vue')
}
```

## 使用说明

### 开发环境运行
```bash
npm run dev
```

### 访问页面
- 用户列表：http://localhost:5173/users
- 用户详情：http://localhost:5173/users/U001
- 个人资料：http://localhost:5173/profile

### 功能测试
1. **用户列表页面**：
   - 测试搜索功能
   - 测试新增用户
   - 测试编辑用户
   - 测试状态切换
   - 测试分页功能

2. **用户详情页面**：
   - 查看用户完整信息
   - 测试编辑功能
   - 查看统计数据
   - 查看订单和余额记录

3. **个人资料页面**：
   - 编辑个人信息
   - 修改密码
   - 账户充值
   - 快捷操作

## 待完善功能

### 后端接口集成
- 用户CRUD接口
- 用户统计接口
- 订单查询接口
- 余额变动接口
- 充值支付接口

### 功能增强
- 头像上传
- 实名认证
- 消息通知
- 操作日志
- 数据导出

### 性能优化
- 虚拟滚动
- 懒加载
- 缓存策略
- 图片优化

## 注意事项

1. **数据安全**: 敏感信息需要加密传输
2. **权限控制**: 需要实现用户权限验证
3. **表单验证**: 前后端双重验证
4. **错误处理**: 完善的错误提示和处理
5. **响应式**: 适配移动端显示

## 贡献指南

1. 遵循Vue 3最佳实践
2. 使用Element UI组件规范
3. 保持代码风格一致
4. 添加必要的注释
5. 编写单元测试
